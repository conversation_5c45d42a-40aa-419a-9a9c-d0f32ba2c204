<template>
  <section class="gps warm-card">
    <!-- 顶部绿色状态胶囊：对齐 iOS -->
    <div class="status-pill" :data-variant="statusColor">
      <div class="left">
        <span class="dot"></span>
        <span class="title">{{ statusText }}</span>
        <template v-if="weather?.obsTime">
          <span class="sep">·</span>
          <span class="time">{{ relativeObsTime }}</span>
        </template>
        <span v-if="error" class="err">（{{ error }}）</span>
      </div>
      <div class="right">
        <!-- 右侧禁用图标占位：播放 + 魔法棒（精致） -->
        <button class="icon-btn" disabled title="播放"><PlayIcon :size="12" /></button>
        <button class="icon-btn wand" disabled title="魔法">✨</button>
      </div>
    </div>

    <!-- 信息气泡：仅在非加载且有最新数据时显示，避免展示旧数据 -->
    <div v-if="!loading && weather" class="bubble" role="group">
      <span class="i-weather"></span>
      <span class="text">{{ weather.text }}</span>
      <span class="seg"> 体感 {{ weather.feelsLike }}度</span>
      <span v-if="weather.humidity" class="seg"> 湿度 {{ weather.humidity }}%</span>
      <span class="seg">{{ weather.windDir }} {{ weather.windScale }}级</span>
    </div>
  </section>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import type { ICurrentWeather } from '@/adapters/weatherAdapter'
import PlayIcon from '@/components/common/PlayIcon.vue'

interface IProps {
  weather?: ICurrentWeather | null
  loading?: boolean
  error?: string
}

const props = withDefaults(defineProps<IProps>(), {
  weather: null,
  loading: false,
  error: '',
})

const statusText = computed(() => {
  if (props.loading) return '获取天气中...'
  if (props.error) return '天气获取错误'
  if (props.weather) return '当前地点天气'
  return '天气未获取'
})

const statusColor = computed(() => {
  if (props.loading) return 'c-orange'
  if (props.error) return 'c-red'
  if (props.weather) return 'c-green'
  return 'c-gray'
})

// 相对时间：将 obsTime（ISO）转为“X秒前/分钟前/小时前”
const relativeObsTime = computed(() => {
  const t = props.weather?.obsTime
  if (!t) return ''
  const obs = new Date(t).getTime()
  if (Number.isNaN(obs)) return t
  const diff = Math.max(0, Date.now() - obs)
  const sec = Math.floor(diff / 1000)
  if (sec < 60) return `${sec}秒前`
  const min = Math.floor(sec / 60)
  if (min < 60) return `${min}分钟前`
  const hr = Math.floor(min / 60)
  return `${hr}小时前`
})
</script>

<style scoped>
.gps { padding: 20px; }

/* 顶部状态胶囊样式（使用设计系统样式） */
.status-pill {
  display:flex; align-items:center; justify-content:space-between;
  padding: 12px 16px; border-radius: 16px;
  border: 2px solid var(--border-accent);
  background: var(--bg-glass);
  box-shadow: var(--shadow-soft);
  backdrop-filter: blur(10px);
  margin-bottom: 12px;
  transition: all 0.3s ease;
}
.status-pill:hover {
  background: var(--bg-glass-hover);
  transform: translateY(-1px);
}
.status-pill[data-variant="c-orange"] { border-color: rgba(255,152,0,0.25); background: linear-gradient(180deg, rgba(255,152,0,0.12), rgba(255,152,0,0.06)); }
.status-pill[data-variant="c-red"] { border-color: rgba(229,57,53,0.25); background: linear-gradient(180deg, rgba(229,57,53,0.12), rgba(229,57,53,0.06)); }
.status-pill[data-variant="c-gray"] { border-color: rgba(158,158,158,0.25); background: linear-gradient(180deg, rgba(158,158,158,0.12), rgba(158,158,158,0.06)); }
.left { display:flex; align-items:center; gap:6px; min-width:0; }
.dot { width:8px; height:8px; border-radius:50%; display:inline-block; background:#4caf50; }
.status-pill[data-variant="c-orange"] .dot { background:#ff9800 }
.status-pill[data-variant="c-red"] .dot { background:#e53935 }
.status-pill[data-variant="c-gray"] .dot { background:#9e9e9e }
.title { color: #000000; font-size: 12px; font-weight: 600; }
.sep { opacity: 0.6; }
.time { color: #000000; font-size: 11px; display:inline-flex; align-items:center; gap:4px; }
.err { color: #e53935; font-size: 12px; }
.right { display:flex; align-items:center; gap:6px; }
.icon-btn { width:22px; height:22px; border-radius:11px; border:1px solid rgba(0,0,0,0.06); background: rgba(255,255,255,0.75); color:#2e7d32; display:inline-flex; align-items:center; justify-content:center; opacity:0.85; -webkit-appearance: none; appearance: none; line-height: 0; }
.icon-btn .play-icon { display:block; fill: currentColor; }
.icon-btn:disabled { cursor:not-allowed }

/* 信息气泡容器 */
.bubble { display:flex; align-items:center; flex-wrap:nowrap; gap:8px; padding:12px 16px; border:2px solid var(--border-accent); border-radius:16px; background: var(--bg-glass); backdrop-filter: blur(10px); overflow:hidden; transition: all 0.3s ease; }
.bubble:hover { background: var(--bg-glass-hover); }
.bubble > * { white-space: nowrap; }
.bubble .i-weather { width:14px; height:14px; border-radius:7px; background:#2e7d32 }
.bubble .text { color: #000000; font-size: 13px; font-weight:600 }
.bubble .seg { opacity: 0.95; font-size:12px; color: #000000; font-weight: 500; }
.bubble .dot { opacity: 0.5 }

/* 简易图标占位（播放图标改为 PlayIcon 组件） */
.i-wand, .i-weather {
  width:14px; height:14px; display:inline-block; background: currentColor; mask-size: cover; -webkit-mask-size: cover;
}
/* 占位用纯形状（小圆/矩形），可后续替换为 SF Symbol 风格 SVG */
.i-clock { border-radius:50%; box-shadow: inset 0 0 0 2px currentColor; background: transparent; }
.i-wand { position:relative; width:12px; height:12px; }
.i-wand::before { content:""; position:absolute; left:5px; top:1px; width:2px; height:10px; background: currentColor; transform: rotate(28deg); border-radius:2px; }
.i-wand::after { content:""; position:absolute; left:2px; top:0; width:4px; height:4px; border-radius:50%; background: currentColor; opacity: 0.9; }
.i-weather { width:12px; height:12px; border-radius:6px; background: #2e7d32; }
</style>
