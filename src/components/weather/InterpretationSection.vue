<template>
  <section class="interpret warm-card">
    <!-- 标题栏：深解读（副标题在右侧） -->
    <div class="header">
      <div class="left-row">
        <span class="title">深解读</span>
        <span class="subtitle">天气数据➕个人情况</span>
        <button aria-label="帮助" class="info-plain" @click="showingHelp = true">i</button>
      </div>
      <div class="right">
        <button class="icon-btn" title="语音播放"><PlayIcon :size="12" /></button>
        <button class="icon-btn wand" title="小魔仙">✨</button>
      </div>
    </div>

    <!-- 帮助弹层（轻量实现） -->
    <div v-if="showingHelp" class="alert">
      <div class="alert-card">
        <div class="alert-title">深度解读模块说明</div>
        <div class="alert-body">基于 Thinking 模型进行解读，结合天气信息与用户个人信息（来自本模块上方的备忘录），速度较慢，通常要 20～30 秒。</div>
        <div class="alert-actions">
          <button class="btn" @click="showingHelp = false">了解</button>
        </div>
      </div>
    </div>

    <!-- 内容区域（对齐 iOS 的多状态渲染） -->
    <div class="content">
      <!-- 加载步骤 -->
      <template v-if="loading && !loadingFinished">
        <div class="steps">
          <div v-for="(step, i) in loadingSteps" :key="i" class="step">
            <span class="step-icon" :class="{ pending: step.includes('正在') }">{{ step.includes('正在') ? '…' : '✓' }}</span>
            <span class="step-text">{{ step }}</span>
          </div>
        </div>
      </template>

      <!-- 多城市解读卡片列表（占位样式） -->
      <template v-else-if="cityInterpretations.length">
        <div class="cities">
          <div v-for="(ci, idx) in cityInterpretations" :key="ci.id || idx" class="city-card">
            <div class="city-header">
              <div class="left">
                <span class="city-name">{{ ci.cityName || '城市' }}</span>
                <button class="icon-btn" title="语音播放"><PlayIcon :size="12" /></button>
                <button class="icon-btn wand" title="小魔仙">✨</button>
              </div>
              <div class="right">
                <button class="icon-btn pale" title="详情" aria-label="查看详情" @click="onDetails(ci)">📃</button>
                <button class="icon-btn pale" title="复制" aria-label="复制内容" @click="onCopy(ci)">📋</button>
                <button class="icon-btn pale" title="转发" aria-label="系统分享" @click="onShare(ci)">📤</button>
              </div>
            </div>
            <div class="body">
              <div class="city-text">{{ ci.text || '（解读内容稍后接入）' }}</div>
            </div>
          </div>
        </div>
      </template>

      <!-- 单条解读文本 -->
      <template v-else-if="interpretationText">
        <div class="single-text">{{ interpretationText }}</div>
      </template>

      <!-- 初始加载提示 -->
      <template v-else-if="loading && !loadingSteps.length">
        <div class="loading-row">
          <span class="spinner">◌</span>
          <span class="loading-text">正在加载数据...</span>
        </div>
      </template>

      <!-- 通用加载提示 -->
      <template v-else-if="loading">
        <div class="loading-row">
          <span class="spinner">◌</span>
          <span class="loading-text">正在加载数据...</span>
        </div>
      </template>

      <!-- 空态 -->
      <template v-else>
        <div class="empty">暂无天气解读，请下拉刷新或添加备忘录后重试。</div>
      </template>
    </div>
  </section>
  
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useShare } from '@/composables/useShare'
import PlayIcon from '@/components/common/PlayIcon.vue'

// 轻量内部状态（后续可由服务层注入/props 替代）
const showingHelp = ref(false)

// 通用分享/复制/详情处理
const { copyWithToast, shareWithFallback, showDetailsPlaceholder } = useShare()

// 类型对齐 iOS 的 CityInterpretation 语义
type CityInterpretation = { id?: string|number; cityName?: string; text?: string }

function buildShareText(ci: CityInterpretation) {
  const name = ci?.cityName || '城市'
  const text = ci?.text || ''
  return `【深解读】${name}\n${text}`
}
function onDetails(ci: CityInterpretation) { showDetailsPlaceholder() }
async function onCopy(ci: CityInterpretation) { await copyWithToast(buildShareText(ci)) }
async function onShare(ci: CityInterpretation) { await shareWithFallback(`深解读 - ${ci?.cityName || ''}`.trim(), buildShareText(ci)) }

// 对外暴露的可选 props（若未传入，则使用本地占位回退）
const props = defineProps<{
  loading?: boolean
  loadingFinished?: boolean
  loadingSteps?: string[]
  cityInterpretations?: CityInterpretation[]
  interpretationText?: string
}>()

// 本地占位（默认空态），用于 props 未提供时回退
const localLoading = ref(false)
const localLoadingFinished = ref(false)
const localLoadingSteps = ref<string[]>([])
const localCityInterpretations = ref<CityInterpretation[]>([])
const localInterpretationText = ref('')

// 统一对外使用的计算值：优先 props，回退本地
const loading = computed(() => props.loading ?? localLoading.value)
const loadingFinished = computed(() => props.loadingFinished ?? localLoadingFinished.value)
const loadingSteps = computed(() => props.loadingSteps ?? localLoadingSteps.value)
const cityInterpretations = computed(() => props.cityInterpretations ?? localCityInterpretations.value)
const interpretationText = computed(() => props.interpretationText ?? localInterpretationText.value)

// 可加入计算属性或对接服务，这里先保留结构
const hasAnyContent = computed(() => !!interpretationText.value || cityInterpretations.value.length > 0)
</script>

<style scoped>
.interpret { padding: 20px; border-radius: 20px; border: 2px solid var(--border-accent); background: var(--bg-glass); backdrop-filter: blur(10px); transition: all 0.3s ease; }
.interpret:hover { background: var(--bg-glass-hover); transform: translateY(-1px); }
.header { display:flex; align-items:center; gap:12px; padding:16px 20px; border-radius:20px; border:2px solid var(--border-accent); background: var(--bg-glass); backdrop-filter: blur(10px); transition: all 0.3s ease; }
.header:hover { background: var(--bg-glass-hover); transform: translateY(-1px); }
.left-row { display:flex; align-items:center; gap:8px; }
.title { font-size: 18px; font-weight: 700; color: #000000; }
.subtitle { font-size: 14px; color: #000000; font-weight:600; opacity: 0.8; }
.right { margin-left:auto; display:flex; gap:6px; }
.info-plain { background: transparent; border: none; color: #2f6feb; font-weight: 700; padding: 0 4px; line-height: 1; border-radius: 3px; }
.info-plain:hover { filter: brightness(0.95); }
.icon-btn { width:22px; height:22px; border-radius:11px; border:1px solid rgba(0,0,0,0.06); background: rgba(255,255,255,0.9); display:inline-flex; align-items:center; justify-content:center; color:#2e7d32 }
.icon-btn.wand { color:#DAA520 }
.icon.info { border: 1px solid var(--border); background: rgba(255,255,255,0.9); color: #000000; padding: 2px 6px; border-radius: 6px; font-size:12px }

.alert { position: fixed; inset: 0; background: rgba(0,0,0,0.25); display:flex; align-items:center; justify-content:center; z-index: 20; }
.alert-card { width: min(480px, 88vw); background: var(--card-bg); border: 1px solid var(--border); border-radius: 10px; padding: 14px; }
.alert-title { font-size: 14px; font-weight: 600; color: #000000; margin-bottom: 8px; }
.alert-body { font-size: 13px; color: #000000; line-height: 1.6; }
.alert-actions { display:flex; justify-content:flex-end; margin-top: 12px; }
.btn { padding: 6px 12px; border-radius: 6px; border: 1px solid var(--border); background: var(--card-bg); color: #000000; }

.content { margin-top: 8px; }
.steps { display:flex; flex-direction:column; gap:8px; padding: 8px 0; }
.step { display:flex; align-items:center; gap:8px; }
.step-icon { width:16px; display:inline-flex; align-items:center; justify-content:center; color: #000000; }
.step-icon.pending { color: var(--primary, #f90); }
.step-text { font-size: 13px; color: #000000; }

.cities { display:flex; flex-direction:column; gap:8px; }
.city-card { padding:8px; border:1px solid var(--border); border-radius:10px; background: transparent; overflow:hidden; }
.city-header { display:flex; align-items:center; gap:8px; margin-bottom:0; padding:6px 8px; border-radius:8px 8px 0 0; background: linear-gradient(180deg, rgba(240,248,255,0.85), rgba(240,248,255,0.65)); }
.city-header .left { display:flex; align-items:center; gap:6px }
.city-header .right { margin-left:auto; display:flex; gap:6px }
.city-name { font-size: 13px; font-weight: 600; color: #000000; }
.icon-btn { width:20px; height:20px; border-radius:10px; border:1px solid rgba(0,0,0,0.06); background: rgba(255,255,255,0.9); color:#2e7d32; display:inline-flex; align-items:center; justify-content:center; }
.icon-btn { -webkit-appearance: none; appearance: none; line-height: 0; }
.icon-btn .play-icon { display:block; fill: currentColor; }
.icon-btn.pale { color:#607d8b }
.icon-btn.wand { color:#DAA520 }
.body { padding:6px 8px; border-radius:0 0 8px 8px; background: linear-gradient(180deg, rgba(255,255,255,0.95), rgba(250,252,255,0.9)); }
.city-text { font-size: 13px; color: #000000; line-height: 1.6; }

.single-text { font-size: 14px; color: #000000; line-height: 1.6; padding: 8px 0; }

.loading-row { display:flex; align-items:center; gap:8px; padding: 8px 0; color: #000000; }
.spinner { display:inline-block; width: 10px; text-align:center; }
.loading-text { font-size: 13px; }

.empty { font-size: 13px; color: #000000; padding: 8px 0; }

/* 简易图标占位，与其他模块保持一致（播放图标改为 PlayIcon 组件） */
.i-wand { width:12px; height:12px; display:inline-block; position:relative; background: transparent }
.i-wand::before { content:""; position:absolute; left:5px; top:1px; width:2px; height:10px; background: currentColor; transform: rotate(28deg); border-radius:2px }
.i-wand::after { content:""; position:absolute; left:2px; top:0; width:4px; height:4px; border-radius:50%; background: currentColor; opacity:.9 }
</style>
