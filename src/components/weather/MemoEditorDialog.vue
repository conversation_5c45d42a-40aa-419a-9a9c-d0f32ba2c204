<template>
  <div v-if="modelValue" class="overlay" @click.self="onClose">
    <div class="panel warm-card">
      <h3 class="title">{{ isEditing ? '编辑备忘录' : '添加备忘录' }}</h3>
      <!-- 引导文案（对齐 iOS）：简短说明用途 -->
      <div class="guide">
        <div class="guide-title">填写与你相关的关键信息，AI 解读会更懂你</div>
        <div class="guide-desc">例如居住地、通勤方式、健康状况、临时出行计划等；可从下方示例直接点击填写</div>
      </div>
      <!-- 示例轮播（每批 8 条，每 5 秒切换） -->
      <div class="samples" role="list">
        <button
          v-for="s in samplesBatch"
          :key="s"
          class="chip"
          role="listitem"
          type="button"
          @click="applySample(s)"
        >
          {{ s }}
        </button>
      </div>
      <textarea v-model="text" class="input" rows="5"></textarea>
      <div class="actions">
        <button class="btn" @click="onClose">取消</button>
        <button v-if="isEditing" class="btn danger" @click="onDeleteClick">删除</button>
        <button class="btn primary" :disabled="!text.trim()" @click="emit('save', text.trim())">{{ isEditing ? '保存修改' : '添加' }}</button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// 简单弹窗：用于新增/编辑备忘录（无语音）
import { computed, onUnmounted, ref, watch } from 'vue'
import type { SmartMemo } from '@/stores/memoStore'
import { memoSamples, getCurrentBatch, getTotalBatches } from '@/data/memoSamples'

interface IProps {
  modelValue: boolean
  editingMemo?: SmartMemo | null
  initialText?: string
}
const props = withDefaults(defineProps<IProps>(), {
  editingMemo: null,
  initialText: '',
})
const emit = defineEmits<{
  (e: 'update:modelValue', v: boolean): void
  (e: 'save', text: string): void
  (e: 'delete'): void
}>()

const text = ref('')
const isEditing = computed(() => !!props.editingMemo)

// 示例轮播相关（对齐 iOS：5 秒切换一批 8 条，循环）
const currentBatchIndex = ref(0)
const samplesBatch = ref<string[]>(getCurrentBatch(memoSamples, 0, 8))
let timer: number | null = null

function startSampleRotation() {
  stopSampleRotation()
  timer = window.setInterval(() => {
    const total = getTotalBatches(memoSamples, 8)
    currentBatchIndex.value = (currentBatchIndex.value + 1) % total
    samplesBatch.value = getCurrentBatch(memoSamples, currentBatchIndex.value * 8, 8)
  }, 5000)
}

function stopSampleRotation() {
  if (timer) {
    clearInterval(timer)
    timer = null
  }
}

function applySample(s: string) {
  text.value = s
}

watch(
  () => props.modelValue,
  (v) => {
    if (v) {
      text.value = props.editingMemo?.text ?? props.initialText ?? ''
      // 打开时重置批次并开始轮播
      currentBatchIndex.value = 0
      samplesBatch.value = getCurrentBatch(memoSamples, 0, 8)
      if (!isEditing.value) startSampleRotation()
    } else {
      // 关闭时停止轮播
      stopSampleRotation()
    }
  },
  { immediate: true }
)

function onClose() {
  emit('update:modelValue', false)
}

function onDeleteClick() {
  // eslint-disable-next-line no-alert
  if (confirm('确认删除？')) {
    emit('delete')
  }
}

onUnmounted(() => {
  stopSampleRotation()
})
</script>

<style scoped>
.overlay { position: fixed; inset: 0; background: rgba(0,0,0,0.35); display:flex; align-items:center; justify-content:center; z-index: 60; }
.panel { width: min(520px, 92vw); padding: 12px; }
.title { margin: 0 0 8px; font-size: 15px; font-weight: 700; color: #000000; }
.guide { margin: 6px 0 8px; color: #000000; }
.guide-title { font-size: 12px; font-weight: 600; }
.guide-desc { font-size: 12px; opacity: 0.9; }
.samples { display: flex; gap: 8px; overflow-x: auto; padding-bottom: 6px; }
.chip {
  flex: 0 0 auto;
  padding: 6px 10px;
  border-radius: 12px;
  border: 1px solid color-mix(in srgb, var(--border) 80%, transparent);
  background: linear-gradient(180deg, rgba(255,255,255,0.96), rgba(255,255,255,0.88));
  color: #000000;
  font-size: 13px;
  box-shadow:
    0 1px 1px rgba(0,0,0,0.03),
    0 2px 6px rgba(0,0,0,0.04);
}
.input { width: 100%; padding: 8px; border-radius: 8px; border: 1px solid var(--border); resize: vertical; background: var(--card-bg); color: #000000; }
.actions { margin-top: 10px; display: flex; justify-content: flex-end; gap: 8px; }
.btn { padding: 6px 10px; border: 1px solid var(--border); background: var(--card-bg); color: #000000; border-radius: 8px; }
.primary { background: var(--primary); color: #fff; border-color: transparent; }
.danger { background: #ff6b6b; color: #fff; border-color: transparent; }
</style>
